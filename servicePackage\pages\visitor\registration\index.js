// 访客登记表单页面

const VisitorManager = require('@/utils/visitor-manager');
const dateUtil = require('@/utils/dateUtil');
const util = require('@/utils/util');
 // 调用API保存访客数据
 const visitorsApi = require('@/api/visitorsApi.js');
Page({
  data: {



    formData: {
      visitorName: '',
      visitorPhone: '',
      visitDate: '',
      visitTime: '',
      duration: 2, // 默认滞留时长2小时
      purpose: '',
      carNumber: '',
      remarks: ''
    },
    errors: {}, // 表单错误信息

    // 日期时间选择器相关
    showDateTimePicker: false,
    dateArray: [], // 日期数组
    hourArray: [], // 小时数组
    minuteArray: [], // 分钟数组
    dateTimePickerValue: [0, 0, 0], // 选择器当前值

    // 滞留时长选择器相关
    showDurationPicker: false,
    durationOptions: [1, 2, 3, 4, 8, 12, 24, 48], // 滞留时长选项

    // 来访目的选择器相关
    showPurposePicker: false,
    purposeOptions: ['探亲访友', '业务洽谈', '送货上门', '维修服务', '其他'],

    // 车牌号历史记录
    showCarNumberHistory: false,
    carNumberHistory: [],

    // 常用访客相关
    showFrequentVisitorsModal: false,
    frequentVisitors: [],

    // 隐私协议
    showPrivacyModal: false
  },

  onLoad: function() {



    // 初始化表单数据
    this.initFormData();

    // 初始化日期时间选择器
    this.initDateTimePicker();

    // 获取车牌号历史记录
    this.getCarNumberHistory();

    // 获取常用访客列表
    this.getFrequentVisitors();

    // 首次使用时显示隐私协议
    this.checkPrivacyAgreement();
  },



  // 初始化表单数据
  initFormData: function() {
    // 设置默认来访时间为当前时间+30分钟
    const now = new Date();
    now.setMinutes(now.getMinutes() + 30);

    const visitDate = dateUtil.formatDate(now);
    const visitTime = dateUtil.formatTimeHM(now);

    this.setData({
      'formData.visitDate': visitDate,
      'formData.visitTime': visitTime
    });
  },

  // 初始化日期时间选择器
  initDateTimePicker: function() {
    // 获取未来7天的日期
    const dateArray = dateUtil.getFutureDates(7);

    // 获取小时数组
    const hourArray = dateUtil.getHours();

    // 获取分钟数组（每5分钟一个选项）
    const minuteArray = dateUtil.getMinutes(5);

    // 设置默认选中值
    const now = new Date();
    now.setMinutes(now.getMinutes() + 30);

    const defaultDateIndex = 0; // 默认选中今天
    const defaultHourIndex = now.getHours();
    const defaultMinuteIndex = Math.floor(now.getMinutes() / 5);

    this.setData({
      dateArray: dateArray,
      hourArray: hourArray,
      minuteArray: minuteArray,
      dateTimePickerValue: [defaultDateIndex, defaultHourIndex, defaultMinuteIndex]
    });
  },

  // 获取车牌号历史记录
  getCarNumberHistory: function() {
    const history = VisitorManager.getCarNumberHistory();
    this.setData({
      carNumberHistory: history
    });
  },

  // 检查隐私协议
  checkPrivacyAgreement: function() {
    const hasAgreed = wx.getStorageSync('visitorPrivacyAgreed');
    if (!hasAgreed) {
      this.setData({
        showPrivacyModal: true
      });
    }
  },

  // 同意隐私协议
  agreePrivacy: function() {
    wx.setStorageSync('visitorPrivacyAgreed', true);
    this.setData({
      showPrivacyModal: false
    });
  },

  // 拒绝隐私协议
  rejectPrivacy: function() {
    this.setData({
      showPrivacyModal: false
    });
    // 用户不同意隐私协议，返回上一页
    wx.navigateBack();
  },



  // 表单输入处理函数
  inputVisitorName: function(e) {
    this.setData({
      'formData.visitorName': e.detail.value,
      'errors.visitorName': ''
    });
  },

  inputVisitorPhone: function(e) {
    this.setData({
      'formData.visitorPhone': e.detail.value,
      'errors.visitorPhone': ''
    });
  },

  inputCarNumber: function(e) {
    this.setData({
      'formData.carNumber': e.detail.value,
      'errors.carNumber': ''
    });
  },

  inputRemarks: function(e) {
    this.setData({
      'formData.remarks': e.detail.value
    });
  },

  // 粘贴手机号
  pastePhoneNumber: function() {
    util.pastePhoneNumber(
      (phoneNumber) => {
        this.setData({
          'formData.visitorPhone': phoneNumber,
          'errors.visitorPhone': ''
        });
      },
      () => {
        // 错误处理已在util.pastePhoneNumber中处理
      }
    );
  },

  // 显示日期时间选择器
  showDateTimePicker: function() {
    this.setData({
      showDateTimePicker: true
    });
  },

  // 隐藏日期时间选择器
  hideDateTimePicker: function() {
    this.setData({
      showDateTimePicker: false
    });
  },

  // 日期时间选择器变化
  onDateTimePickerChange: function(e) {
    this.setData({
      dateTimePickerValue: e.detail.value
    });
  },

  // 确认日期时间
  confirmDateTime: function() {
    const { dateArray, hourArray, minuteArray, dateTimePickerValue } = this.data;

    const selectedDate = dateArray[dateTimePickerValue[0]].date;
    const selectedHour = hourArray[dateTimePickerValue[1]];
    const selectedMinute = minuteArray[dateTimePickerValue[2]];

    const selectedTime = `${selectedHour}:${selectedMinute}`;

    this.setData({
      'formData.visitDate': selectedDate,
      'formData.visitTime': selectedTime,
      'errors.visitDateTime': '',
      showDateTimePicker: false
    });
  },

  // 显示滞留时长选择器
  showDurationPicker: function() {
    this.setData({
      showDurationPicker: true
    });
  },

  // 隐藏滞留时长选择器
  hideDurationPicker: function() {
    this.setData({
      showDurationPicker: false
    });
  },

  // 选择滞留时长
  selectDuration: function(e) {
    const duration = parseInt(e.currentTarget.dataset.duration);
    this.setData({
      'formData.duration': duration,
      'errors.duration': ''
    });
  },

  // 确认滞留时长
  confirmDuration: function() {
    this.setData({
      showDurationPicker: false
    });
  },

  // 显示来访目的选择器
  showPurposePicker: function() {
    this.setData({
      showPurposePicker: true
    });
  },

  // 隐藏来访目的选择器
  hidePurposePicker: function() {
    this.setData({
      showPurposePicker: false
    });
  },

  // 选择来访目的
  selectPurpose: function(e) {
    const purpose = e.currentTarget.dataset.purpose;
    this.setData({
      'formData.purpose': purpose,
      'errors.purpose': '',
      showPurposePicker: false
    });
  },

  // 显示车牌号历史记录
  showCarNumberHistory: function() {
    this.setData({
      showCarNumberHistory: true
    });
  },

  // 隐藏车牌号历史记录
  hideCarNumberHistory: function() {
    this.setData({
      showCarNumberHistory: false
    });
  },



  // 表单提交
  submitForm: function() {
    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    // 构建访客数据
    const visitorData = this.buildVisitorData();
     
    // 显示加载状态
    wx.showLoading({
      title: '正在创建访客...'
    });


    visitorsApi.addVisitor(visitorData).then(res => {
      wx.hideLoading();
      console.log('创建访客结果：', res);

   
        // 创建成功
        wx.showToast({
          title: '访客创建成功',
          icon: 'success'
        });

     
        // 跳转到访客凭证页面
        wx.navigateTo({
          url: `/servicePackage/pages/visitor/credential/index?id=${res}`
        });
     
    }).catch(err => {
      wx.hideLoading();
      console.error('创建访客异常：', err);
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
    });
  },

  // 表单验证
  validateForm: function() {
    const { visitorName, visitorPhone, visitDate, visitTime, duration, purpose, carNumber } = this.data.formData;
  
    let errors = {};
    let isValid = true;

    // 验证访客姓名
    if (!visitorName.trim()) {
      errors.visitorName = '请输入访客姓名';
      isValid = false;
    }

    // 验证手机号
    if (!visitorPhone) {
      errors.visitorPhone = '请输入手机号码';
      isValid = false;
    } else if (!util.validatePhone(visitorPhone)) {
      errors.visitorPhone = '请输入正确的手机号码';
      isValid = false;
    }

    // 验证来访时间
    const visitDateTime = dateUtil.parseDateTime(visitDate, visitTime);
     
    const now = new Date();
    if (visitDateTime < now) {
      errors.visitDateTime = '来访时间不能早于当前时间';
      isValid = false;
    }

    // 验证滞留时长
    if (!duration || duration <= 0) {
      errors.duration = '请选择有效的滞留时长';
      isValid = false;
    }

    // 验证来访目的
    if (!purpose) {
      errors.purpose = '请选择来访目的';
      isValid = false;
    }

    // 验证车牌号
    if (carNumber && !util.validateCarNumber(carNumber)) {
      errors.carNumber = '请输入正确的车牌号码';
      isValid = false;
    
    }

    this.setData({ errors });
    return isValid;
  },

  // 构建访客数据
  buildVisitorData: function() {
    const { visitorName, visitorPhone, visitDate, visitTime, duration, purpose, carNumber, remarks } = this.data.formData;
 
    // 构建访问时间（ISO格式）
    const visitDateTime = new Date(`${visitDate} ${visitTime}`);

    // 获取社区信息
    const userInfo = wx.getStorageSync('userInfo') || {}
    const selectedCommunity = wx.getStorageSync('selectedCommunity') || {}
    const communityId = selectedCommunity.id || userInfo.communityId || 1 // 默认社区ID

    return {
      visitorName: visitorName,
      phone: visitorPhone,
      vehicleNumber: carNumber || '',
      note: remarks || purpose || '', // 将备注或目的作为note字段
      stayDuration: duration, // 停留时长（小时）
      timeUnit: 'hour', // 时间单位
      visitTime: dateUtil.formatISOToDateTime(visitDateTime), // ISO格式的访问时间
      communityId: communityId
    };
  },

  // 获取常用访客列表
  getFrequentVisitors: function() {
    const frequentVisitors = VisitorManager.getFrequentVisitors();
    this.setData({
      frequentVisitors: frequentVisitors
    });
  },

  // 显示常用访客选择弹窗
  showFrequentVisitors: function() {
    this.getFrequentVisitors(); // 刷新列表
    this.setData({
      showFrequentVisitorsModal: true
    });
  },

  // 隐藏常用访客选择弹窗
  hideFrequentVisitors: function() {
    this.setData({
      showFrequentVisitorsModal: false
    });
  },

  // 选择常用访客
  selectFrequentVisitor: function(e) {
    const id = e.currentTarget.dataset.id;
    const frequentVisitor = this.data.frequentVisitors.find(v => v.id === id);

    if (frequentVisitor) {
      // 填充表单数据
      this.setData({
        'formData.visitorName': frequentVisitor.name,
        'formData.visitorPhone': frequentVisitor.phone,
        'errors.visitorName': '',
        'errors.visitorPhone': '',
        showFrequentVisitorsModal: false
      });

      // 如果有来访目的，也填充
      if (frequentVisitor.purpose) {
        this.setData({
          'formData.purpose': frequentVisitor.purpose,
          'errors.purpose': ''
        });
      }

      // 如果有车牌号，也填充
      if (frequentVisitor.carNumber) {
        this.setData({
          'formData.carNumber': frequentVisitor.carNumber,
          'errors.carNumber': ''
        });

      }

      wx.showToast({
        title: '已填充常用访客信息',
        icon: 'success'
      });
    }
  },


  // 导航到批量邀请页面
  navigateToBatchInvite: function() {
    wx.navigateTo({
      url: '/servicePackage/pages/visitor/batch-invite/index'
    });
  },

  // 导航返回
  navigateBack: function() {
    wx.navigateBack();
  }
});
