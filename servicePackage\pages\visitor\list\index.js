// 我的访客列表页面
const VisitorManager = require('@/utils/visitor-manager')
const visitorsApi = require('@/api/visitorsApi')
const dateUtil = require('@/utils/dateUtil')
const util = require('@/utils/util')
Page({
  data: {

    visitors: [], // 所有访客数据
    filteredVisitors: [], // 筛选后的访客数据
    visitorGroups: { // 分组后的访客数据
      today: [],
      tomorrow: [],
      week: [],
      earlier: []
    },

    loading:false,
    // 筛选相关
    currentFilter: 'all', // 当前筛选条件
    filterTabs: [ // 筛选标签数组，从字典动态获取
      { nameEn: 'all', nameCn: '全部' }
    ],
    searchKeyword: '', // 搜索关键词

    currentPage: 1, // 当前页码
    pageSize: 10,

    hasMoreVisitors: false, // 是否有更多访客
    scrollIntoView: '', // 滚动定位ID
    showExtendModal: false, // 是否显示延期弹窗
    currentVisitorId: '', // 当前操作的访客ID
    statusTextMap: {} // 状态文本映射，从字典获取
  },

  onLoad: function () {
    this.getVisitorStatus()
  },

  getVisitorStatus() {
    try {
      const visitorStatusDict = util.getDictByNameEn('visitor_status')
      if (visitorStatusDict && visitorStatusDict.length > 0 && visitorStatusDict[0].children) {
        // 将字典数据转换为状态映射对象
        const statusMap = {}
        const filterTabs = [
          { nameEn: 'all', nameCn: '全部' } // 默认添加"全部"选项
        ]

        visitorStatusDict[0].children.forEach(item => {
          statusMap[item.nameEn] = item.nameCn
          // 添加到筛选标签数组
          filterTabs.push({
            nameEn: item.nameEn,
            nameCn: item.nameCn
          })
        })

        this.setData({
          statusTextMap: statusMap,
          filterTabs: filterTabs
        })

        console.log('访客状态字典：', statusMap)
        console.log('筛选标签：', filterTabs)
      } else {
        // 如果字典数据不存在，使用默认数据
        const defaultStatusMap = {
          'wait_visit': '待到访',
          'visited': '已到访',
          'expire': '已过期'
        }
        const defaultFilterTabs = [
          { nameEn: 'all', nameCn: '全部' },
          { nameEn: 'wait_visit', nameCn: '待到访' },
          { nameEn: 'visited', nameCn: '已到访' },
          { nameEn: 'expire', nameCn: '已过期' }
        ]

        this.setData({
          statusTextMap: defaultStatusMap,
          filterTabs: defaultFilterTabs
        })
      }
    } catch (error) {
      console.error('获取访客状态字典失败：', error)
      // 使用默认数据
      const defaultStatusMap = {
        'wait_visit': '待到访',
        'visited': '已到访',
        'expire': '已过期'
      }
      const defaultFilterTabs = [
        { nameEn: 'all', nameCn: '全部' },
        { nameEn: 'wait_visit', nameCn: '待到访' },
        { nameEn: 'visited', nameCn: '已到访' },
        { nameEn: 'expire', nameCn: '已过期' }
      ]

      this.setData({
        statusTextMap: defaultStatusMap,
        filterTabs: defaultFilterTabs
      })
    }
  },

  onShow: function () {
    this.onPullDownRefresh()
  },

  // 加载访客数据
  loadVisitors: function () {
    const { currentFilter, currentPage, pageSize } = this.data
    const params = {
      pageNum: currentPage,
      pageSize: pageSize,
      communityId: wx.getStorageSync('selectedCommunity').id,
    }

    // 根据筛选条件添加status参数
    if (currentFilter && currentFilter !== 'all') {
      params.status = currentFilter
    }

    console.log('加载访客数据，参数：', params)

    visitorsApi.getVisitorList(params).then(res => {
      console.log('访客列表', res);

      if (res) {
        // 处理分页数据或直接数组数据
        const visitors = Array.isArray(res.list) ? res.list :
          Array.isArray(res.list) ? res.list : [];

        // 处理访客数据，只保留API接口返回的字段
        const processedVisitors = visitors.map(visitor => {
          // 使用dateUtil格式化访问时间
          const formattedVisitTime = dateUtil.formatISOToDateTime(visitor.visitTime)

          // 计算结束时间并格式化
          const visitTimeDate = new Date(visitor.visitTime)
          const endTimeDate = new Date(visitTimeDate.getTime() + visitor.stayDuration * 60 * 60 * 1000)
          const formattedEndTime = dateUtil.formatISOToDateTime(endTimeDate.toISOString())

          return {
            id: visitor.id,
            visitorName: visitor.visitorName,
            phone: visitor.phone,
            vehicleNumber: visitor.vehicleNumber || '',
            note: visitor.note || '',
            stayDuration: visitor.stayDuration,
            timeUnit: visitor.timeUnit || 'hour',
            visitTime: formattedVisitTime, // 格式化后的访问时间
            endTime: formattedEndTime, // 格式化后的结束时间
            originalVisitTime: visitor.visitTime, // 保留原始ISO时间用于计算
            communityId: visitor.communityId,
            status: visitor.status || 'wait_visit', // 默认状态为待到访
            isUsual: visitor.isUsual || false, // 是否收藏
            createTime: visitor.createTime,
            updateTime: visitor.updateTime
          }
        })

        // 处理分页数据合并
        let allVisitors = processedVisitors;
        if (currentPage > 1) {
          // 如果是加载更多，合并数据
          allVisitors = [...this.data.visitors, ...processedVisitors];
        }

        // 检查是否还有更多数据
        const hasMoreVisitors = res.list ?
          (res.list.length === pageSize) :
          (processedVisitors.length === pageSize);

        this.setData({
          visitors: allVisitors,
          hasMoreVisitors: hasMoreVisitors
        });

        // 保存到访客管理器（用于本地操作）
        VisitorManager.setAllVisitors(allVisitors)

        // 应用筛选和搜索
        this.applyFilterAndSearch();
      } else {
        console.error('获取访客列表失败：', res);
        this.setData({
          visitors: []
        });

        wx.showToast({
          title: res.message || '获取访客列表失败',
          icon: 'none'
        });
      }
    })
    .catch(err => {
      console.error('获取访客列表异常：', err);
      this.setData({
        visitors: []
      });
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
    });
  },

  // 检查访客状态
  checkVisitorsStatus: function () {
    // 检查并更新访客状态
    if (VisitorManager.checkVisitorsStatus()) {
      // 如果有更新，重新加载数据
      this.loadVisitors();
    }
  },

  // 应用筛选和搜索
  applyFilterAndSearch: function () {
    const { visitors, searchKeyword } = this.data;
    let filteredVisitors = visitors;

    // 如果有搜索关键词，在前端进行搜索筛选
    if (searchKeyword) {
      const lowerKeyword = searchKeyword.toLowerCase();
      filteredVisitors = visitors.filter(visitor =>
        visitor.visitorName.toLowerCase().includes(lowerKeyword) ||
        visitor.phone.includes(lowerKeyword) ||
        (visitor.note && visitor.note.toLowerCase().includes(lowerKeyword)) ||
        (visitor.vehicleNumber && visitor.vehicleNumber.toLowerCase().includes(lowerKeyword))
      );
    }

    // 分组数据
    const groupedVisitors = VisitorManager.groupVisitors(filteredVisitors);

    this.setData({
      filteredVisitors: filteredVisitors,
      visitorGroups: groupedVisitors
    });

    // 设置默认滚动位置
    this.setDefaultScrollPosition();
  },

  // 设置默认滚动位置
  setDefaultScrollPosition: function () {
    const { visitorGroups } = this.data;

    // 默认滚动到今天的访客
    if (visitorGroups.today.length > 0) {
      this.setData({
        scrollIntoView: 'group-today'
      });
    } else if (visitorGroups.tomorrow.length > 0) {
      this.setData({
        scrollIntoView: 'group-tomorrow'
      });
    } else if (visitorGroups.week.length > 0) {
      this.setData({
        scrollIntoView: 'group-week'
      });
    } else if (visitorGroups.earlier.length > 0) {
      this.setData({
        scrollIntoView: 'group-earlier'
      });
    }
  },

  // 设置筛选条件
  setFilter: function (e) {
    const filter = e.currentTarget.dataset.filter;

    this.setData({
      currentFilter: filter,
      currentPage: 1 // 重置页码
    });

    // 重新加载数据（根据筛选条件从接口获取）
    this.loadVisitors();
  },

  // 搜索输入
  onSearchInput: function (e) {
    this.setData({
      searchKeyword: e.detail.value
    });

    // 应用搜索（在当前已加载的数据中搜索）
    this.applyFilterAndSearch();
  },

  // 清除搜索
  clearSearch: function () {
    this.setData({
      searchKeyword: ''
    });

    // 应用筛选（在当前已加载的数据中搜索）
    this.applyFilterAndSearch();
  },

  // 加载更多访客
  loadMoreVisitors: function () {
    if (!this.data.hasMoreVisitors) {
      return;
    }

    this.setData({
      currentPage: this.data.currentPage + 1
    });

    // 加载更多数据
    this.loadVisitors();
  },

  // 查看访客详情
  viewVisitorDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/servicePackage/pages/visitor/credential/index?id=${id}`
    });
  },

  // 查看访客凭证
  viewCredential: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/servicePackage/pages/visitor/credential/index?id=${id}`
    });
  },

  // 分享访客
  shareVisitor: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/servicePackage/pages/visitor/credential/index?id=${id}&share=true`
    });
  },

  // 显示延期选项
  showExtendOptions: function (e) {
    const id = e.currentTarget.dataset.id;

    this.setData({
      showExtendModal: true,
      currentVisitorId: id
    });
  },

  // 隐藏延期弹窗
  hideExtendModal: function () {
    this.setData({
      showExtendModal: false,
      currentVisitorId: ''
    });
  },

  // 延长访客时间
  extendVisitor: function (e) {
    const hours = parseInt(e.currentTarget.dataset.hours);
    const { currentVisitorId } = this.data;

    if (!currentVisitorId) {
      return;
    }

    // 找到当前访客数据
    const currentVisitor = this.data.visitors.find(visitor => visitor.id === currentVisitorId);
    if (!currentVisitor) {
      wx.showToast({
        title: '访客信息不存在',
        icon: 'none'
      });
      return;
    }

    // 准备API参数
    const params = {
      id: currentVisitorId,
      stayDuration: hours,
      timeUnit: "hours",
      visitTime: currentVisitor.originalVisitTime
    };

    console.log('延期访客参数：', params);

    // 显示加载状态
    wx.showLoading({
      title: '延期中...',
      mask: true
    });

    // 调用延期访客接口
    visitorsApi.editVisitTime(params)
      .then(res => {
        console.log('延期访客结果：', res);

        wx.hideLoading();

        // 延期成功
        this.hideExtendModal();

        wx.showToast({
          title: `已延长${hours}小时`,
          icon: 'success'
        });

        // 重新加载访客列表
        this.loadVisitors();
      })
      .catch(err => {
        console.error('延期访客失败：', err);

        wx.hideLoading();

   
      });
  },


  // 删除访客
  deleteVisitor: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.showModal({
      title: '删除访客',
      content: '确定要删除此访客记录吗？',
      confirmText: '删除',
      confirmColor: '#FF3B30',
      success: (res) => {
        if (res.confirm) {



          // 调用API删除访客记录
          visitorsApi.deleteVisitor(id).then(result => {
            console.log('删除访客结果：', result);

          
              // 删除成功，重新加载数据
              this.loadVisitors();

              // 提示成功
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
           
         
          }).catch(err => {
            console.error('删除访客异常：', err);
      
          });





        }
      }
    });
  },

  // 跳转到访客登记页面
  navigateToRegistration: function () {
    wx.navigateTo({
      url: '/servicePackage/pages/visitor/registration/index'
    });
  },

  // 切换收藏状态
  toggleFavorite: function (e) {
    const id = e.currentTarget.dataset.id;

    // 找到当前访客数据
    const currentVisitor = this.data.visitors.find(visitor => visitor.id === id);
    if (!currentVisitor) {
      wx.showToast({
        title: '访客信息不存在',
        icon: 'none'
      });
      return;
    }

    // 切换收藏状态
    const newIsUsual = !currentVisitor.isUsual;

    // 准备API参数
    const params = {
      id: id,
      isUsual: newIsUsual
    };

    console.log('切换收藏状态参数：', params);

    // 调用设为常用访客接口
    visitorsApi.editVisitUse(params)
      .then(res => {
        console.log('切换收藏状态结果：', res);

        // 更新本地数据
        const updatedVisitors = this.data.visitors.map(visitor => {
          if (visitor.id === id) {
            console.log('更新访客收藏状态：', visitor.visitorName, 'isUsual:', visitor.isUsual, '->', newIsUsual);
            return { ...visitor, isUsual: newIsUsual };
          }
          return visitor;
        });

        console.log('更新后的访客列表：', updatedVisitors.map(v => ({id: v.id, name: v.visitorName, isUsual: v.isUsual})));

        this.setData({
          visitors: updatedVisitors
        });

        // 重新应用筛选和搜索
        this.applyFilterAndSearch();

        wx.showToast({
          title: newIsUsual ? '已收藏' : '已取消收藏',
          icon: 'success'
        });
      })
      .catch(err => {
        console.error('切换收藏状态失败：', err);

        wx.showToast({
          title: err.message || '操作失败，请重试',
          icon: 'none'
        });
      });
  },

  // 设为常用访客
  saveAsFrequent: function (e) {
    const id = e.currentTarget.dataset.id;

    // 获取访客数据
    const visitorData = VisitorManager.getVisitorById(id);
    if (!visitorData) {
      wx.showToast({
        title: '获取访客信息失败',
        icon: 'none'
      });
      return;
    }

    // 解密敏感信息
    const decryptedVisitor = VisitorManager.decryptVisitorData(visitorData);

    // 保存为常用访客
    if (VisitorManager.saveFrequentVisitor(decryptedVisitor)) {
      wx.showToast({
        title: '已设为常用访客',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '设置失败，请重试',
        icon: 'none'
      });
    }
  },

  // 导航返回
  navigateBack: function () {
    wx.navigateBack();
  },



  onPullDownRefresh: function() {
    // 下拉刷新 - 重置分页并重新加载
    this.setData({
      pageNum: 1,
      visitors:[],
      filteredVisitors:[],
      visitorGroups: { // 分组后的访客数据
        today: [],
        tomorrow: [],
        week: [],
        earlier: []
      }
    })
  

    // 每次显示页面时刷新数据
    this.loadVisitors();

    // 检查访客状态（过期等）
    this.checkVisitorsStatus();
    
    setTimeout(() => {
      this.setData({loading:false})
    }, 1500);

  },

});
